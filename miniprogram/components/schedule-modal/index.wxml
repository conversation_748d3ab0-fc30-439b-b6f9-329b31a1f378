<!-- 设置工作计划模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">设置日期安排</view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>
    
    <view class="modal-body">
      <view class="modal-date">{{selectedDateText}}</view>
      
      <!-- 日期状态设置 -->
      <view class="input-group">
        <view class="input-label">日期类型</view>
        <view class="status-selector" bind:tap="onOpenDateTypeSelector">
          <view class="picker-text">
            <text class="status-emoji">{{selectedStatusConfig.icon}}</text>
            <text>{{selectedStatusConfig.text}}</text>
            <text class="selector-arrow">›</text>
          </view>
        </view>
      </view>
      
      <!-- 时间段设置 -->
      <view class="input-group">
        <view class="input-label">
          <text>日期安排</text>
          <view class="time-summary">
            <view class="smart-income-btn" bind:tap="onSmartIncomeCalculate">
              <text class="btn-icon">✨</text>
              <text>智能填写</text>
            </view>
          </view>
        </view>

        <!-- 时间段问题提示 -->
        <view class="conflict-warning" wx:if="{{hasTimeConflict}}">
          <text class="warning-icon">⚠️</text>
          <text class="warning-text">检测到时间段设置问题，请调整后再保存</text>
        </view>

        <!-- 时间段列表 -->
        <view class="time-inputs" wx:if="{{timeInputs.length > 0}}">
          <view class="time-segment-card segment-{{item.type}} {{item.hasConflict ? 'conflict' : ''}}" wx:for="{{timeInputs}}" wx:key="index">
            <!-- 时间段头部 - 类型、时间、删除按钮在同一行 -->
            <view class="segment-header">
              <!-- 时间段类型标签 -->
              <view class="segment-type-tag">
                <picker class="segment-type-picker"
                        range="{{typeOptions}}"
                        range-key="text"
                        value="{{item.typeIndex}}"
                        bind:change="onTypeChange"
                        data-index="{{index}}">
                  <view class="segment-type-display">
                    <text class="type-icon">{{typeOptions[item.typeIndex].icon || '🕐'}}</text>
                    <text class="type-text">{{typeOptions[item.typeIndex].text}}</text>
                  </view>
                </picker>
              </view>

              <!-- 时间范围选择器 -->
              <view class="segment-time-range" bind:tap="onOpenTimeRangePicker" data-index="{{index}}">
                <view class="time-range-text">
                  <text class="start-time">{{item.startTime}}</text>
                  <text class="start-next-day-indicator" wx:if="{{item.isStartNextDay}}">次日</text>
                  <text class="time-separator">-</text>
                  <text class="end-time">{{item.endTime}}</text>
                  <text class="end-next-day-indicator" wx:if="{{item.isEndNextDay}}">次日</text>
                </view>
                <view class="time-duration" wx:if="{{item.durationText}}">
                  <text class="duration-text">{{item.durationText}}</text>
                </view>
              </view>

              <!-- 删除按钮 -->
              <view class="segment-remove-btn" bind:tap="onDeleteTimeInput" data-index="{{index}}">
                <text class="remove-icon">×</text>
              </view>
            </view>

            <!-- 收入设置区域 -->
            <view class="income-setting-row" wx:if="{{item.type !== 'rest'}}">
              <view class="income-field">
                <text class="income-field-label">收入</text>
                <view class="income-input-wrapper">
                  <input class="income-input-field"
                         type="digit"
                         placeholder="100"
                         value="{{item.incomeText || item.income}}"
                         bind:input="onIncomeChange"
                         data-index="{{index}}" />
                  <text class="income-unit">元</text>
                </view>
              </view>

              <view class="hourly-rate-field">
                <text class="hourly-rate-label">时薪</text>
                <view class="hourly-rate-input-wrapper">
                  <input class="hourly-rate-input-field"
                         type="digit"
                         placeholder="60"
                         value="{{item.hourlyRateText || item.hourlyRate}}"
                         bind:input="onHourlyRateChange"
                         bind:focus="onStartEditingHourlyRate"
                         bind:blur="onFinishEditingHourlyRate"
                         data-index="{{index}}" />
                  <text class="hourly-rate-unit">/小时</text>
                </view>
              </view>
            </view>

            <!-- 时间段问题提示 -->
            <view class="segment-warning" wx:if="{{item.hasConflict}}">
              <text class="warning-icon">⚠️</text>
              <text class="warning-message">{{item.warningMessage || '时间设置有问题'}}</text>
            </view>
          </view>
        </view>

        <view class="add-time-btn" bind:tap="onAddTimeInput">
          <text class="add-icon">➕</text>
          <text>添加时间段</text>
        </view>
      </view>
    </view>

    <!-- 统计信息区域 -->
    <view class="schedule-summary">
      <view class="summary-item">
        <text class="summary-label">工作</text>
        <text class="summary-value">{{workHoursText}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">休息</text>
        <text class="summary-value">{{restHoursText}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">加班</text>
        <text class="summary-value">{{overtimeHoursText}}</text>
      </view>
      <view class="summary-item income-item">
        <text class="summary-label">收入</text>
        <text class="summary-value income-value">{{totalIncomeText}}元</text>
      </view>
    </view>

    <view class="modal-footer">
      <view class="btn-secondary modal-btn" bind:tap="onImportFromDateInModal">
        <text class="btn-icon">📥</text>
        <text>导入安排</text>
      </view>
      <view class="btn-primary modal-btn {{hasTimeConflict ? 'disabled' : ''}}"
            bind:tap="onConfirmSchedule">
        <text>{{hasTimeConflict ? '无法保存' : '保存'}}</text>
      </view>
    </view>
  </view>

  <!-- 子组件 -->
  <!-- 日期类型选择器 -->
  <date-type-selector
    show="{{showDateTypeSelector}}"
    value="{{dateStatus}}"
    bind:confirm="onDateTypeSelectorConfirm"
    bind:cancel="onDateTypeSelectorCancel"
    bind:close="onDateTypeSelectorClose">
  </date-type-selector>

  <!-- 智能收入模态框 -->
  <smart-income-modal
    visible="{{showSmartIncomeModal}}"
    time-inputs="{{timeInputs}}"
    bind:confirm="onSmartIncomeConfirm"
    bind:cancel="onSmartIncomeCancel"
    bind:close="onSmartIncomeClose">
  </smart-income-modal>

  <!-- 工作安排导入模态框 -->
  <schedule-import-modal
    visible="{{showScheduleImportModal}}"
    target-date="{{selectedDate}}"
    work-id="{{currentWorkId}}"
    bind:confirm="onScheduleImportConfirm"
    bind:cancel="onScheduleImportCancel"
    bind:close="onScheduleImportClose">
  </schedule-import-modal>

  <!-- 时间范围选择器 -->
  <time-range-picker
    show="{{showTimeRangePicker}}"
    start-time="{{timeRangeStartTime}}"
    end-time="{{timeRangeEndTime}}"
    is-start-next-day="{{timeRangeIsStartNextDay}}"
    is-end-next-day="{{timeRangeIsEndNextDay}}"
    bind:confirm="onTimeRangePickerConfirm"
    bind:cancel="onTimeRangePickerCancel"
    bind:close="onTimeRangePickerClose">
  </time-range-picker>
</view>
